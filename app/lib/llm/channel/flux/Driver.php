<?php

namespace app\lib\llm\channel\flux;

use app\lib\llm\Exception;
use Psr\Http\Message\ResponseInterface;
use think\helper\Arr;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = 'Flux';

    protected function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $content = $response->getBody()->getContents();
        $result = json_decode($content, true);

        if ($statusCode !== 200) {
            $message = 'Unknown error';
            if ($result) {
                // 处理不同类型的错误响应
                $message = Arr::get($result, 'error.message',
                          Arr::get($result, 'message',
                          Arr::get($result, 'detail',
                          Arr::get($result, 'error', $message))));

                // 处理特定的错误状态码
                if ($statusCode === 429) {
                    $message = 'Rate limit exceeded. Please wait and try again.';
                } elseif ($statusCode === 402) {
                    $message = 'Insufficient credits. Please add credits to your account.';
                } elseif ($statusCode === 401) {
                    $message = 'Invalid API key. Please check your authentication.';
                }
            }
            throw new Exception($message);
        }

        return $result;
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://api.bfl.ai'),
            'headers'  => [
                'x-key' => $this->getAuth(),
                'Content-Type' => 'application/json',
                'accept' => 'application/json',
            ],
        ];
    }
}
