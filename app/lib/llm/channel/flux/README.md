# Flux 渠道对接文档

## 概述

Flux 是 Black Forest Labs 开发的先进图像生成模型，支持文本到图像生成和图像编辑功能。本渠道实现了对 Flux API 的完整对接。

## 支持的模型

- **flux-kontext-pro**: 支持文本到图像生成和图像编辑
- **flux-kontext-max**: 支持文本到图像生成和图像编辑（限制更严格）
- **flux-pro-1.1-ultra**: 高质量文本到图像生成
- **flux-pro-1.1**: 标准质量文本到图像生成
- **flux-pro**: 基础文本到图像生成
- **flux-dev**: 开发版本，支持更多参数控制

## 功能特性

### 文本到图像生成
- 支持多种宽高比（3:7 到 7:3）
- 种子控制，确保结果可重现
- 提示词增强（Kontext 模型）
- 安全级别控制
- 多种输出格式（JPEG/PNG）

### 图像编辑（仅 Kontext 模型）
- 基于文本提示的图像编辑
- 保持图像一致性
- 支持文本替换
- 迭代编辑能力

## API 配置

### 认证
需要在渠道配置中设置 Flux API Key：

```php
'auth' => 'your-flux-api-key'
```

### 基础 URL
默认使用 `https://api.bfl.ai`，可通过 `base_uri` 配置项覆盖。

## 使用示例

### 基础图像生成

```php
$result = $llm->image()->generations([
    'model'  => 'flux-kontext-pro',
    'prompt' => 'A small furry elephant pet looks out from a cat house',
    'size'   => '1:1',
]);
```

### 带参数的图像生成

```php
$result = $llm->image()->generations([
    'model'  => 'flux-kontext-pro',
    'prompt' => 'A futuristic cityscape at night',
    'size'   => '16:9',
    'seed'   => 42,
    'prompt_upsampling' => true,
    'safety_tolerance' => 3,
    'output_format' => 'png',
]);
```

### 图像编辑

```php
$result = $llm->image()->edit([
    'model'  => 'flux-kontext-pro',
    'prompt' => 'Change the car color to red',
    'image'  => 'https://example.com/image.jpg',
    'size'   => '4:3',
]);
```

## 支持的参数

### 通用参数
- `prompt`: 文本提示词（必需）
- `size`: 图像尺寸，支持宽高比格式（如 "16:9"）或传统格式（如 "1024x768"）
- `seed`: 随机种子，用于结果重现
- `response_format`: 响应格式，目前仅支持 "url"

### Kontext 模型特有参数
- `prompt_upsampling`: 是否启用提示词增强
- `safety_tolerance`: 安全级别（0-6，默认2）
- `output_format`: 输出格式（"jpeg" 或 "png"）

### Pro 模型特有参数
- `quality`: 图像质量设置

### Dev 模型特有参数
- `steps`: 生成步数
- `guidance`: 引导强度

### 图像编辑特有参数
- `image`: 输入图像（必需），支持 URL 或 base64 格式
- `input_image`: 同 `image`，base64 编码的图像数据

## 支持的尺寸

### 宽高比格式
- `1:1` - 正方形（1024x1024）
- `4:3` - 横向（1024x768）
- `3:4` - 纵向（768x1024）
- `16:9` - 宽屏（1024x576）
- `9:16` - 竖屏（576x1024）
- `3:2` - 横向（1024x682）
- `2:3` - 纵向（682x1024）
- `7:3` - 超宽（1024x439）
- `3:7` - 超高（439x1024）

### 传统格式
系统会自动将传统的 "宽x高" 格式转换为对应的宽高比。

## 错误处理

渠道实现了完善的错误处理机制：

- **429**: 请求频率限制，建议等待后重试
- **402**: 余额不足，需要充值
- **401**: API Key 无效
- **其他错误**: 返回具体的错误信息

## 异步处理

Flux API 采用异步设计：
1. 提交请求获得任务 ID
2. 轮询任务状态直到完成
3. 返回生成的图像 URL

轮询间隔为 0.5 秒，最大等待时间为 5 分钟。

## 注意事项

1. 生成的图像 URL 有效期为 10 分钟，请及时下载
2. 图像编辑功能仅支持 Kontext 模型
3. 输入图像最大支持 20MB 或 2000 万像素
4. 所有输出图像约为 1MP 总像素
