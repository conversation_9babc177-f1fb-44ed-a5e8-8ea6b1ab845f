<?php

namespace tests\llm\flux;

use tests\llm\TestCase;

class ImageTest extends TestCase
{
    use WithEnv;

    public function testGenerations()
    {
        $result = $this->llm->image()->generations([
            'model'  => 'flux-kontext-pro',
            'prompt' => 'A small furry elephant pet looks out from a cat house',
            'size'   => '1:1',
        ]);

        dump($result);

        $this->assertArrayHasKey('images', $result);
        $this->assertArrayHasKey('usage', $result);
        $this->assertNotEmpty($result['images']);
        $this->assertArrayHasKey('url', $result['images'][0]);
    }

    public function testGenerationsWithSeed()
    {
        $result = $this->llm->image()->generations([
            'model'  => 'flux-kontext-pro',
            'prompt' => 'A cute cat sitting on a windowsill',
            'size'   => '16:9',
            'seed'   => 12345,
        ]);

        dump($result);

        $this->assertArrayHasKey('images', $result);
        $this->assertArrayHasKey('usage', $result);
    }

    public function testGenerationsWithDifferentModel()
    {
        $result = $this->llm->image()->generations([
            'model'  => 'flux-pro-1.1',
            'prompt' => 'Abstract expressionist painting of a vibrant sunset',
            'size'   => '3:4',
        ]);

        dump($result);

        $this->assertArrayHasKey('images', $result);
        $this->assertArrayHasKey('usage', $result);
    }

    public function testGenerationsWithTraditionalSize()
    {
        $result = $this->llm->image()->generations([
            'model'  => 'flux-kontext-pro',
            'prompt' => 'A beautiful landscape with mountains and lakes',
            'size'   => '1024x768', // 传统尺寸格式
        ]);

        dump($result);

        $this->assertArrayHasKey('images', $result);
        $this->assertArrayHasKey('usage', $result);
    }

    public function testGenerationsWithAdvancedParams()
    {
        $result = $this->llm->image()->generations([
            'model'  => 'flux-kontext-pro',
            'prompt' => 'A futuristic cityscape at night',
            'size'   => '16:9',
            'seed'   => 42,
            'prompt_upsampling' => true,
            'safety_tolerance' => 3,
            'output_format' => 'png',
        ]);

        dump($result);

        $this->assertArrayHasKey('images', $result);
        $this->assertArrayHasKey('usage', $result);
    }

    public function testEdit()
    {
        $result = $this->llm->image()->edit([
            'model'  => 'flux-kontext-pro',
            'prompt' => 'Change the car color to red',
            'image'  => 'https://example.com/test-image.jpg', // 需要替换为实际的图片URL
            'size'   => '1:1',
        ]);

        dump($result);

        $this->assertArrayHasKey('images', $result);
        $this->assertArrayHasKey('usage', $result);
        $this->assertNotEmpty($result['images']);
        $this->assertArrayHasKey('url', $result['images'][0]);
    }

    public function testEditWithAdvancedParams()
    {
        $result = $this->llm->image()->edit([
            'model'  => 'flux-kontext-pro',
            'prompt' => 'Replace "joy" with "BFL"',
            'image'  => 'https://example.com/sign-image.jpg', // 需要替换为实际的图片URL
            'size'   => '4:3',
            'seed'   => 123,
            'prompt_upsampling' => true,
            'safety_tolerance' => 1,
            'output_format' => 'png',
        ]);

        dump($result);

        $this->assertArrayHasKey('images', $result);
        $this->assertArrayHasKey('usage', $result);
    }
}
